package com.example.anna.util

import kotlin.math.*

/**
 * Mathematical utility functions for prediction calculations
 */
object MathUtils {
    
    /**
     * Calculates the standard deviation of a list of numbers
     */
    fun standardDeviation(values: List<Double>): Double {
        if (values.isEmpty()) return 0.0
        
        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        return sqrt(variance)
    }
    
    /**
     * Calculates weighted average where more recent values have higher weights
     */
    fun weightedAverage(values: List<Double>, decayFactor: Double = 0.1): Double {
        if (values.isEmpty()) return 0.0
        
        var weightedSum = 0.0
        var totalWeight = 0.0
        
        values.forEachIndexed { index, value ->
            val weight = exp(-index * decayFactor)
            weightedSum += value * weight
            totalWeight += weight
        }
        
        return if (totalWeight > 0) weightedSum / totalWeight else 0.0
    }
    
    /**
     * Normalizes a value to a 0-1 range using min-max normalization
     */
    fun normalize(value: Double, min: Double, max: Double): Double {
        return if (max > min) {
            (value - min) / (max - min)
        } else {
            0.5 // Return neutral value if range is invalid
        }
    }
    
    /**
     * Applies sigmoid function to convert any real number to 0-1 range
     */
    fun sigmoid(x: Double, steepness: Double = 1.0): Double {
        return 1.0 / (1.0 + exp(-x * steepness))
    }
    
    /**
     * Calculates Poisson probability for a given lambda and k
     */
    fun poissonProbability(lambda: Double, k: Int): Double {
        return (lambda.pow(k) * exp(-lambda)) / factorial(k)
    }
    
    /**
     * Calculates factorial of a number (up to 20 for performance)
     */
    private fun factorial(n: Int): Double {
        return when {
            n <= 1 -> 1.0
            n <= 20 -> (2..n).fold(1.0) { acc, i -> acc * i }
            else -> throw IllegalArgumentException("Factorial calculation limited to n <= 20")
        }
    }
    
    /**
     * Calculates confidence interval for a given confidence level
     */
    fun confidenceInterval(
        mean: Double, 
        standardDeviation: Double, 
        sampleSize: Int, 
        confidenceLevel: Double = 0.95
    ): Pair<Double, Double> {
        val zScore = when (confidenceLevel) {
            0.90 -> 1.645
            0.95 -> 1.96
            0.99 -> 2.576
            else -> 1.96 // Default to 95%
        }
        
        val marginOfError = zScore * (standardDeviation / sqrt(sampleSize.toDouble()))
        return Pair(mean - marginOfError, mean + marginOfError)
    }
    
    /**
     * Calculates correlation coefficient between two datasets
     */
    fun correlation(x: List<Double>, y: List<Double>): Double {
        if (x.size != y.size || x.isEmpty()) return 0.0
        
        val meanX = x.average()
        val meanY = y.average()
        
        val numerator = x.zip(y).sumOf { (xi, yi) -> (xi - meanX) * (yi - meanY) }
        val denominatorX = x.sumOf { (it - meanX).pow(2) }
        val denominatorY = y.sumOf { (it - meanY).pow(2) }
        
        return if (denominatorX > 0 && denominatorY > 0) {
            numerator / sqrt(denominatorX * denominatorY)
        } else {
            0.0
        }
    }
    
    /**
     * Calculates moving average over a specified window
     */
    fun movingAverage(values: List<Double>, windowSize: Int): List<Double> {
        if (values.size < windowSize) return emptyList()
        
        return values.windowed(windowSize).map { window ->
            window.average()
        }
    }
    
    /**
     * Calculates exponential moving average
     */
    fun exponentialMovingAverage(values: List<Double>, alpha: Double = 0.3): List<Double> {
        if (values.isEmpty()) return emptyList()
        
        val result = mutableListOf<Double>()
        var ema = values.first()
        result.add(ema)
        
        for (i in 1 until values.size) {
            ema = alpha * values[i] + (1 - alpha) * ema
            result.add(ema)
        }
        
        return result
    }
    
    /**
     * Calculates the trend direction of a series of values
     * Returns: 1.0 for strong upward trend, -1.0 for strong downward trend, 0.0 for no trend
     */
    fun calculateTrend(values: List<Double>): Double {
        if (values.size < 2) return 0.0
        
        val indices = values.indices.map { it.toDouble() }
        val correlation = correlation(indices, values)
        
        return correlation.coerceIn(-1.0, 1.0)
    }
    
    /**
     * Applies smoothing to reduce noise in data
     */
    fun smoothData(values: List<Double>, smoothingFactor: Double = 0.3): List<Double> {
        if (values.isEmpty()) return emptyList()
        
        val smoothed = mutableListOf<Double>()
        smoothed.add(values.first())
        
        for (i in 1 until values.size) {
            val smoothedValue = smoothingFactor * values[i] + (1 - smoothingFactor) * smoothed[i - 1]
            smoothed.add(smoothedValue)
        }
        
        return smoothed
    }
}
