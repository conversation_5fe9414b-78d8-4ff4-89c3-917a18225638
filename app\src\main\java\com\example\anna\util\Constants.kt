package com.example.anna.util

object Constants {
    // API Base URL for API-Football from RapidAPI
    const val BASE_URL = "https://api-football-v1.p.rapidapi.com/v3/"
    
    // API key is stored in local.properties and accessed via BuildConfig
    // See build.gradle.kts for setup
    
    // Endpoints
    const val FIXTURES_ENDPOINT = "fixtures"
    const val LEAGUES_ENDPOINT = "leagues"
    const val TEAMS_ENDPOINT = "teams"
    const val PLAYERS_ENDPOINT = "players"
    const val STATISTICS_ENDPOINT = "statistics"
    const val HEAD_TO_HEAD_ENDPOINT = "fixtures/headtohead"
    
    // Query Parameters
    const val PARAM_LEAGUE = "league"
    const val PARAM_SEASON = "season"
    const val PARAM_TEAM = "team"
    const val PARAM_PLAYER = "player" 
    const val PARAM_FIXTURE = "fixture"
    const val PARAM_DATE = "date"
    const val PARAM_STATUS = "status"
    const val PARAM_FROM = "from"
    const val PARAM_TO = "to"
    const val PARAM_H2H = "h2h"
    
    // Default values
    const val DEFAULT_SEASON = "2024" // Current soccer season
}
