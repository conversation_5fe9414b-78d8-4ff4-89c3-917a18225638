package com.example.anna.data.model

data class League(
    val id: Int,
    val name: String,
    val type: String,
    val logo: String,
    val country: String,
    val countryCode: String?,
    val countryFlag: String?,
    val season: Int,
    val round: String?
)

data class LeagueWithSeasons(
    val league: League,
    val seasons: List<Season>
)

data class Season(
    val year: Int,
    val start: String,
    val end: String,
    val current: Boolean,
    val coverage: Coverage
)

data class Coverage(
    val fixtures: FixtureCoverage,
    val standings: <PERSON><PERSON><PERSON>,
    val players: <PERSON><PERSON><PERSON>,
    val topScorers: <PERSON><PERSON><PERSON>,
    val topAssists: <PERSON><PERSON><PERSON>,
    val topCards: <PERSON><PERSON><PERSON>,
    val injuries: <PERSON><PERSON><PERSON>,
    val predictions: <PERSON><PERSON>an,
    val odds: <PERSON>olean
)

data class FixtureCoverage(
    val events: <PERSON>olean,
    val lineups: <PERSON><PERSON>an,
    val statisticsFixtures: <PERSON>olean,
    val statisticsPlayers: Boolean
)

data class Standing(
    val rank: Int,
    val team: TeamMatchInfo,
    val points: Int,
    val goalsDiff: Int,
    val group: String,
    val form: String,
    val status: String,
    val description: String?,
    val all: TeamStats,
    val home: TeamStats,
    val away: TeamStats,
    val update: String
)

data class TeamStats(
    val played: Int,
    val win: Int,
    val draw: Int,
    val lose: Int,
    val goals: TeamGoals
)

data class TeamGoals(
    val for_: Int,
    val against: Int
)
