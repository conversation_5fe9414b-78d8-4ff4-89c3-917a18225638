package com.example.anna.data.repository

import com.example.anna.data.model.MatchPrediction
import kotlinx.coroutines.runBlocking

/**
 * Example usage of the PredictionEngine class
 * 
 * This demonstrates how to use the PredictionEngine to generate match predictions
 * for soccer matches using various analytical factors.
 */
class PredictionEngineUsageExample(
    private val predictionEngine: PredictionEngine
) {
    
    /**
     * Example: Generate prediction for a Premier League match
     */
    fun generatePremierLeagueMatchPrediction(): MatchPrediction? {
        return runBlocking {
            try {
                // Example: Manchester City (ID: 50) vs Liverpool (ID: 40)
                // Premier League ID: 39, Season: 2024
                val result = predictionEngine.generatePrediction(
                    homeTeamId = 50,      // Manchester City
                    awayTeamId = 40,      // Liverpool  
                    leagueId = 39,        // Premier League
                    season = "2024",
                    fixtureId = 12345
                )
                
                result.getOrNull()?.also { prediction ->
                    printPredictionSummary(prediction)
                }
            } catch (e: Exception) {
                println("Error generating prediction: ${e.message}")
                null
            }
        }
    }
    
    /**
     * Example: Generate prediction for a La Liga match
     */
    fun generateLaLigaMatchPrediction(): MatchPrediction? {
        return runBlocking {
            try {
                // Example: Real Madrid (ID: 541) vs Barcelona (ID: 529)
                // La Liga ID: 140, Season: 2024
                val result = predictionEngine.generatePrediction(
                    homeTeamId = 541,     // Real Madrid
                    awayTeamId = 529,     // Barcelona
                    leagueId = 140,       // La Liga
                    season = "2024",
                    fixtureId = 67890
                )
                
                result.getOrNull()?.also { prediction ->
                    printPredictionSummary(prediction)
                }
            } catch (e: Exception) {
                println("Error generating prediction: ${e.message}")
                null
            }
        }
    }
    
    /**
     * Prints a formatted summary of the match prediction
     */
    private fun printPredictionSummary(prediction: MatchPrediction) {
        println("\n=== MATCH PREDICTION ===")
        println("${prediction.homeTeamName} vs ${prediction.awayTeamName}")
        println("Fixture ID: ${prediction.fixtureId}")
        
        println("\n--- PROBABILITIES ---")
        println("Home Win: ${String.format("%.1f", prediction.homeWinProbability * 100)}%")
        println("Draw: ${String.format("%.1f", prediction.drawProbability * 100)}%")
        println("Away Win: ${String.format("%.1f", prediction.awayWinProbability * 100)}%")
        
        println("\n--- PREDICTED SCORE ---")
        println("${prediction.homeTeamName}: ${String.format("%.1f", prediction.predictedHomeGoals)} goals")
        println("${prediction.awayTeamName}: ${String.format("%.1f", prediction.predictedAwayGoals)} goals")
        
        println("\n--- PREDICTION ---")
        println("Winner: ${prediction.predictedWinner ?: "Draw"}")
        println("Confidence: ${String.format("%.1f", prediction.confidence * 100)}%")
        
        println("\n--- KEY FACTORS ---")
        printFactorSummary("Recent Form", prediction.predictionFactors.recentFormFactor)
        printFactorSummary("Head-to-Head", prediction.predictionFactors.headToHeadFactor)
        printFactorSummary("Home Advantage", prediction.predictionFactors.homeAdvantage)
        printFactorSummary("Player Strength", prediction.predictionFactors.playerStrength)
        printFactorSummary("Absence Impact", prediction.predictionFactors.absenceImpact)
        
        println("\n--- ADDITIONAL STATS ---")
        prediction.additionalStats.forEach { (key, value) ->
            println("$key: $value")
        }
        
        println("========================\n")
    }
    
    /**
     * Prints a summary of an individual prediction factor
     */
    private fun printFactorSummary(factorName: String, factor: com.example.anna.data.model.Factor) {
        val weight = String.format("%.0f", factor.weight * 100)
        val favoredTeam = factor.favoredTeam ?: "Neutral"
        println("$factorName (${weight}%): $favoredTeam - ${factor.description}")
    }
    
    /**
     * Example: Batch prediction for multiple matches
     */
    fun generateBatchPredictions(matches: List<MatchInfo>): List<MatchPrediction> {
        return runBlocking {
            matches.mapNotNull { match ->
                try {
                    val result = predictionEngine.generatePrediction(
                        homeTeamId = match.homeTeamId,
                        awayTeamId = match.awayTeamId,
                        leagueId = match.leagueId,
                        season = match.season,
                        fixtureId = match.fixtureId
                    )
                    result.getOrNull()
                } catch (e: Exception) {
                    println("Failed to generate prediction for match ${match.fixtureId}: ${e.message}")
                    null
                }
            }
        }
    }
    
    /**
     * Example: Analyze prediction accuracy factors
     */
    fun analyzePredictionFactors(prediction: MatchPrediction) {
        println("\n=== PREDICTION FACTOR ANALYSIS ===")
        
        val factors = listOf(
            prediction.predictionFactors.recentFormFactor,
            prediction.predictionFactors.headToHeadFactor,
            prediction.predictionFactors.homeAdvantage,
            prediction.predictionFactors.playerStrength,
            prediction.predictionFactors.absenceImpact
        )
        
        // Sort factors by weight (importance)
        val sortedFactors = factors.sortedByDescending { it.weight }
        
        println("Factors ranked by importance:")
        sortedFactors.forEachIndexed { index, factor ->
            val rank = index + 1
            val weight = String.format("%.0f", factor.weight * 100)
            val favoredTeam = factor.favoredTeam ?: "Neutral"
            println("$rank. ${factor.name} (${weight}%): Favors $favoredTeam")
            println("   ${factor.description}")
        }
        
        // Calculate total confidence based on factor alignment
        val homeFactors = factors.count { it.favoredTeam == prediction.homeTeamName }
        val awayFactors = factors.count { it.favoredTeam == prediction.awayTeamName }
        val neutralFactors = factors.count { it.favoredTeam == null }
        
        println("\nFactor Alignment:")
        println("Favoring ${prediction.homeTeamName}: $homeFactors factors")
        println("Favoring ${prediction.awayTeamName}: $awayFactors factors")
        println("Neutral: $neutralFactors factors")
        
        println("===================================\n")
    }
}

/**
 * Data class to represent match information for batch processing
 */
data class MatchInfo(
    val fixtureId: Int,
    val homeTeamId: Int,
    val awayTeamId: Int,
    val leagueId: Int,
    val season: String
)

/**
 * Example usage in a real application
 */
fun main() {
    // This would typically be injected via Hilt in a real Android app
    // val predictionEngine = PredictionEngine(footballRepository)
    // val example = PredictionEngineUsageExample(predictionEngine)
    
    // Example batch processing
    val upcomingMatches = listOf(
        MatchInfo(12345, 50, 40, 39, "2024"),    // Man City vs Liverpool
        MatchInfo(12346, 541, 529, 140, "2024"), // Real Madrid vs Barcelona
        MatchInfo(12347, 85, 42, 39, "2024"),    // Arsenal vs Chelsea
    )
    
    // val predictions = example.generateBatchPredictions(upcomingMatches)
    // predictions.forEach { prediction ->
    //     example.analyzePredictionFactors(prediction)
    // }
    
    println("PredictionEngine usage examples ready!")
    println("Uncomment the code above to run with actual PredictionEngine instance.")
}
