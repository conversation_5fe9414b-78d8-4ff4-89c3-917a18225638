package com.example.anna.data.api

import com.example.anna.data.model.ApiResponse
import com.example.anna.data.model.Fixture
import com.example.anna.data.model.LeagueWithSeasons
import com.example.anna.data.model.PlayerDetail
import com.example.anna.data.model.Standing
import com.example.anna.data.model.Team
import com.example.anna.data.model.TeamStatistics
import com.example.anna.util.Constants.FIXTURES_ENDPOINT
import com.example.anna.util.Constants.HEAD_TO_HEAD_ENDPOINT
import com.example.anna.util.Constants.LEAGUES_ENDPOINT
import com.example.anna.util.Constants.PARAM_DATE
import com.example.anna.util.Constants.PARAM_FIXTURE
import com.example.anna.util.Constants.PARAM_FROM
import com.example.anna.util.Constants.PARAM_H2H
import com.example.anna.util.Constants.PARAM_LEAGUE
import com.example.anna.util.Constants.PARAM_PLAYER
import com.example.anna.util.Constants.PARAM_SEASON
import com.example.anna.util.Constants.PARAM_STATUS
import com.example.anna.util.Constants.PARAM_TEAM
import com.example.anna.util.Constants.PARAM_TO
import com.example.anna.util.Constants.PLAYERS_ENDPOINT
import com.example.anna.util.Constants.STATISTICS_ENDPOINT
import com.example.anna.util.Constants.TEAMS_ENDPOINT
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Query

interface FootballApiService {

    @GET(LEAGUES_ENDPOINT)
    suspend fun getLeagues(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query("id") leagueId: Int? = null,
        @Query("country") country: String? = null,
        @Query("current") current: Boolean? = null
    ): Response<ApiResponse<LeagueWithSeasons>>
    
    @GET(TEAMS_ENDPOINT)
    suspend fun getTeams(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_LEAGUE) league: Int? = null,
        @Query(PARAM_SEASON) season: String? = null,
        @Query("id") teamId: Int? = null,
        @Query("country") country: String? = null
    ): Response<ApiResponse<Team>>

    @GET("$TEAMS_ENDPOINT/$STATISTICS_ENDPOINT")
    suspend fun getTeamStatistics(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_LEAGUE) leagueId: Int,
        @Query(PARAM_SEASON) season: String,
        @Query(PARAM_TEAM) teamId: Int
    ): Response<ApiResponse<TeamStatistics>>
    
    @GET(FIXTURES_ENDPOINT)
    suspend fun getFixtures(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_LEAGUE) league: Int? = null,
        @Query(PARAM_SEASON) season: String? = null,
        @Query(PARAM_TEAM) teamId: Int? = null,
        @Query(PARAM_DATE) date: String? = null,  // Format: YYYY-MM-DD
        @Query(PARAM_STATUS) status: String? = null  // NS = Not Started, FT = Finished, etc.
    ): Response<ApiResponse<Fixture>>

    @GET("$FIXTURES_ENDPOINT/statistics")
    suspend fun getFixtureStatistics(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_FIXTURE) fixtureId: Int
    ): Response<ApiResponse<Fixture>>
    
    @GET(HEAD_TO_HEAD_ENDPOINT)
    suspend fun getHeadToHead(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_H2H) h2h: String, // Format: "teamId1-teamId2"
        @Query(PARAM_FROM) from: String? = null, // Format: YYYY-MM-DD
        @Query(PARAM_TO) to: String? = null  // Format: YYYY-MM-DD
    ): Response<ApiResponse<Fixture>>
    
    @GET(PLAYERS_ENDPOINT)
    suspend fun getPlayers(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_TEAM) teamId: Int,
        @Query(PARAM_SEASON) season: String,
        @Query(PARAM_PLAYER) playerId: Int? = null
    ): Response<ApiResponse<PlayerDetail>>
    
    @GET("standings")
    suspend fun getStandings(
        @Header("X-RapidAPI-Key") apiKey: String,
        @Header("X-RapidAPI-Host") host: String = "api-football-v1.p.rapidapi.com",
        @Query(PARAM_LEAGUE) leagueId: Int,
        @Query(PARAM_SEASON) season: String
    ): Response<ApiResponse<List<Standing>>>
}
