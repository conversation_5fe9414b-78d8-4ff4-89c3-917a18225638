package com.example.anna.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.anna.data.model.Fixture
import com.example.anna.databinding.ItemFixtureBinding
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone

class FixtureAdapter(private val onItemClicked: (Fixture) -> Unit) :
    ListAdapter<Fixture, FixtureAdapter.FixtureViewHolder>(FixtureDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FixtureViewHolder {
        val binding = ItemFixtureBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return FixtureViewHolder(binding)
    }

    override fun onBindViewHolder(holder: FixtureViewHolder, position: Int) {
        val fixture = getItem(position)
        holder.bind(fixture)
        holder.itemView.setOnClickListener {
            onItemClicked(fixture)
        }
    }

    inner class FixtureViewHolder(private val binding: ItemFixtureBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(fixture: Fixture) {
            binding.tvHomeTeam.text = fixture.teams.home.name
            binding.tvAwayTeam.text = fixture.teams.away.name
            binding.tvLeague.text = fixture.league.name

            // Format date and time
            try {
                val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.getDefault())
                inputFormat.timeZone = TimeZone.getTimeZone("UTC") // Assuming API returns UTC
                val outputDateFormat = SimpleDateFormat("dd MMM yyyy", Locale.getDefault())
                val outputTimeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                
                val date = inputFormat.parse(fixture.fixture.date)
                if (date != null) {
                    binding.tvMatchDate.text = outputDateFormat.format(date)
                    binding.tvScoreTime.text = outputTimeFormat.format(date) // Show time for upcoming
                } else {
                    binding.tvMatchDate.text = "N/A"
                    binding.tvScoreTime.text = "N/A"
                }
            } catch (e: Exception) {
                binding.tvMatchDate.text = fixture.fixture.date.substringBefore('T') // Fallback
                binding.tvScoreTime.text = fixture.fixture.status.short // Fallback to status if time parsing fails
            }

            // Display score if match is finished or ongoing, otherwise time
            if (fixture.fixture.status.short !in listOf("NS", "TBD", "PST")) { // NS = Not Started, TBD = To Be Defined, PST = Postponed
                binding.tvScoreTime.text = "${fixture.goals.home ?: 0} - ${fixture.goals.away ?: 0}"
            }
        }
    }
}

class FixtureDiffCallback : DiffUtil.ItemCallback<Fixture>() {
    override fun areItemsTheSame(oldItem: Fixture, newItem: Fixture):
        Boolean = oldItem.fixture.id == newItem.fixture.id

    override fun areContentsTheSame(oldItem: Fixture, newItem: Fixture):
        Boolean = oldItem == newItem
}
