package com.example.anna.data.model

/**
 * Generic wrapper class for API responses.
 * API-Football responses typically include a "response" array containing the actual data.
 */
data class ApiResponse<T>(
    val get: String,
    val parameters: Map<String, String>,
    val errors: List<String>,
    val results: Int,
    val paging: Paging,
    val response: List<T>
)

data class Paging(
    val current: Int,
    val total: Int
)
