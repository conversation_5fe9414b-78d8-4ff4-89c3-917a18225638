package com.example.anna.data.repository

import com.example.anna.data.model.*
import com.example.anna.util.Constants
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * Core prediction engine that analyzes various factors to generate match predictions.
 *
 * This engine considers multiple factors:
 * - Team recent form and performance trends
 * - Head-to-head historical records
 * - Home advantage and venue factors
 * - Player strength and key player availability
 * - League position and momentum
 * - Tactical matchups and formations
 */
@Singleton
class PredictionEngine @Inject constructor(
    private val footballRepository: FootballRepository
) {

    companion object {
        // Prediction factor weights (must sum to 1.0)
        private const val RECENT_FORM_WEIGHT = 0.30
        private const val HEAD_TO_HEAD_WEIGHT = 0.20
        private const val HOME_ADVANTAGE_WEIGHT = 0.15
        private const val PLAYER_STRENGTH_WEIGHT = 0.20
        private const val ABSENCE_IMPACT_WEIGHT = 0.10
        private const val LEAGUE_POSITION_WEIGHT = 0.05

        // Analysis parameters
        private const val RECENT_FORM_MATCHES = 5
        private const val HEAD_TO_HEAD_MATCHES = 10
        private const val MIN_CONFIDENCE_THRESHOLD = 0.6
        private const val HOME_ADVANTAGE_BOOST = 0.1

        // Goal prediction constants
        private const val LEAGUE_AVERAGE_GOALS = 2.7
        private const val POISSON_LAMBDA_ADJUSTMENT = 0.85
    }

    /**
     * Generates a comprehensive match prediction for the given fixture
     */
    suspend fun generatePrediction(
        homeTeamId: Int,
        awayTeamId: Int,
        leagueId: Int,
        season: String = Constants.DEFAULT_SEASON,
        fixtureId: Int? = null
    ): Result<MatchPrediction> = try {
        coroutineScope {
            // Fetch all required data concurrently
            val homeTeamDataDeferred = async { getTeamAnalysisData(homeTeamId, leagueId, season) }
            val awayTeamDataDeferred = async { getTeamAnalysisData(awayTeamId, leagueId, season) }
            val headToHeadDeferred = async { getHeadToHeadData(homeTeamId, awayTeamId) }
            val standingsDeferred = async { getLeagueStandings(leagueId, season) }

            val homeTeamData = homeTeamDataDeferred.await().getOrThrow()
            val awayTeamData = awayTeamDataDeferred.await().getOrThrow()
            val headToHeadData = headToHeadDeferred.await().getOrThrow()
            val standings = standingsDeferred.await().getOrThrow()

            // Calculate individual prediction factors
            val recentFormFactor = analyzeRecentForm(homeTeamData, awayTeamData)
            val headToHeadFactor = calculateHeadToHeadFactor(headToHeadData, homeTeamId, awayTeamId)
            val homeAdvantageFactor = calculateHomeAdvantage(homeTeamData.team, awayTeamData.team)
            val playerStrengthFactor = assessPlayerStrength(homeTeamData, awayTeamData)
            val absenceImpactFactor = evaluateAbsenceImpact(homeTeamData, awayTeamData)

            // Combine all factors to generate final prediction
            val prediction = combineFactors(
                homeTeamData = homeTeamData,
                awayTeamData = awayTeamData,
                recentFormFactor = recentFormFactor,
                headToHeadFactor = headToHeadFactor,
                homeAdvantageFactor = homeAdvantageFactor,
                playerStrengthFactor = playerStrengthFactor,
                absenceImpactFactor = absenceImpactFactor,
                standings = standings,
                fixtureId = fixtureId ?: 0
            )

            Result.success(prediction)
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * Fetches and combines all team analysis data needed for prediction
     */
    private suspend fun getTeamAnalysisData(
        teamId: Int,
        leagueId: Int,
        season: String
    ): Result<TeamAnalysisData> = try {
        coroutineScope {
            val teamStatsDeferred = async { footballRepository.getTeamStatistics(leagueId, season, teamId) }
            val recentFixturesDeferred = async {
                footballRepository.getFixtures(
                    league = leagueId,
                    season = season,
                    teamId = teamId
                )
            }
            val playersDeferred = async { footballRepository.getPlayers(teamId, season) }

            val teamStatsResponse = teamStatsDeferred.await()
            val recentFixturesResponse = recentFixturesDeferred.await()
            val playersResponse = playersDeferred.await()

            if (teamStatsResponse.isSuccessful &&
                recentFixturesResponse.isSuccessful &&
                playersResponse.isSuccessful) {

                val teamStats = teamStatsResponse.body()?.response?.firstOrNull()
                val recentFixtures = recentFixturesResponse.body()?.response?.take(RECENT_FORM_MATCHES) ?: emptyList()
                val players = playersResponse.body()?.response ?: emptyList()

                if (teamStats != null) {
                    Result.success(
                        TeamAnalysisData(
                            team = teamStats.team,
                            statistics = teamStats,
                            recentFixtures = recentFixtures,
                            players = players
                        )
                    )
                } else {
                    Result.failure(Exception("Team statistics not found"))
                }
            } else {
                Result.failure(Exception("Failed to fetch team data"))
            }
        }
    } catch (e: Exception) {
        Result.failure(e)
    }

    /**
     * Fetches head-to-head historical data between two teams
     */
    private suspend fun getHeadToHeadData(homeTeamId: Int, awayTeamId: Int): Result<List<Fixture>> = try {
        val response = footballRepository.getHeadToHead(homeTeamId, awayTeamId)
        if (response.isSuccessful) {
            val fixtures = response.body()?.response?.take(HEAD_TO_HEAD_MATCHES) ?: emptyList()
            Result.success(fixtures)
        } else {
            Result.success(emptyList()) // Return empty list if no H2H data available
        }
    } catch (e: Exception) {
        Result.success(emptyList()) // Don't fail prediction if H2H data unavailable
    }

    /**
     * Fetches league standings for position-based analysis
     */
    private suspend fun getLeagueStandings(leagueId: Int, season: String): Result<List<Standing>> = try {
        val response = footballRepository.getStandings(leagueId, season)
        if (response.isSuccessful) {
            val standings = response.body()?.response?.firstOrNull() ?: emptyList()
            Result.success(standings)
        } else {
            Result.success(emptyList())
        }
    } catch (e: Exception) {
        Result.success(emptyList())
    }

    /**
     * Analyzes recent form of both teams and returns comparative factor
     */
    private fun analyzeRecentForm(homeTeam: TeamAnalysisData, awayTeam: TeamAnalysisData): Factor {
        val homeFormScore = calculateFormScore(homeTeam.recentFixtures, homeTeam.team.id)
        val awayFormScore = calculateFormScore(awayTeam.recentFixtures, awayTeam.team.id)

        val formDifference = homeFormScore - awayFormScore
        val favoredTeam = when {
            formDifference > 0.2 -> homeTeam.team.name
            formDifference < -0.2 -> awayTeam.team.name
            else -> null
        }

        return Factor(
            name = "Recent Form",
            weight = RECENT_FORM_WEIGHT,
            favoredTeam = favoredTeam,
            description = when {
                formDifference > 0.2 -> "${homeTeam.team.name} has significantly better recent form"
                formDifference < -0.2 -> "${awayTeam.team.name} has significantly better recent form"
                else -> "Both teams have similar recent form"
            },
            data = mapOf(
                "homeFormScore" to homeFormScore,
                "awayFormScore" to awayFormScore,
                "formDifference" to formDifference,
                "homeRecentResults" to extractFormString(homeTeam.recentFixtures, homeTeam.team.id),
                "awayRecentResults" to extractFormString(awayTeam.recentFixtures, awayTeam.team.id)
            )
        )
    }

    /**
     * Calculates form score based on recent match results
     */
    private fun calculateFormScore(fixtures: List<Fixture>, teamId: Int): Double {
        if (fixtures.isEmpty()) return 0.5 // Neutral score if no data

        var totalScore = 0.0
        var weightSum = 0.0

        fixtures.forEachIndexed { index, fixture ->
            val weight = 1.0 - (index * 0.1) // More recent matches have higher weight
            val matchScore = when {
                fixture.teams.home.id == teamId -> {
                    when {
                        fixture.teams.home.winner == true -> 1.0 // Win
                        fixture.teams.home.winner == false -> 0.0 // Loss
                        else -> 0.5 // Draw
                    }
                }
                fixture.teams.away.id == teamId -> {
                    when {
                        fixture.teams.away.winner == true -> 1.0 // Win
                        fixture.teams.away.winner == false -> 0.0 // Loss
                        else -> 0.5 // Draw
                    }
                }
                else -> 0.5 // Shouldn't happen, but default to neutral
            }

            totalScore += matchScore * weight
            weightSum += weight
        }

        return if (weightSum > 0) totalScore / weightSum else 0.5
    }

    /**
     * Extracts form string (e.g., "WWLDW") from recent fixtures
     */
    private fun extractFormString(fixtures: List<Fixture>, teamId: Int): String {
        return fixtures.take(5).map { fixture ->
            when {
                fixture.teams.home.id == teamId -> {
                    when {
                        fixture.teams.home.winner == true -> "W"
                        fixture.teams.home.winner == false -> "L"
                        else -> "D"
                    }
                }
                fixture.teams.away.id == teamId -> {
                    when {
                        fixture.teams.away.winner == true -> "W"
                        fixture.teams.away.winner == false -> "L"
                        else -> "D"
                    }
                }
                else -> "D"
            }
        }.joinToString("")
    }

    /**
     * Calculates head-to-head factor based on historical matchups
     */
    private fun calculateHeadToHeadFactor(
        headToHeadFixtures: List<Fixture>,
        homeTeamId: Int,
        awayTeamId: Int
    ): Factor {
        if (headToHeadFixtures.isEmpty()) {
            return Factor(
                name = "Head-to-Head",
                weight = HEAD_TO_HEAD_WEIGHT,
                favoredTeam = null,
                description = "No historical data available",
                data = mapOf("matchesAnalyzed" to 0)
            )
        }

        var homeWins = 0
        var awayWins = 0
        var draws = 0
        var homeGoals = 0
        var awayGoals = 0

        headToHeadFixtures.forEach { fixture ->
            val isHomeTeamAtHome = fixture.teams.home.id == homeTeamId

            when {
                fixture.teams.home.winner == true -> {
                    if (isHomeTeamAtHome) homeWins++ else awayWins++
                }
                fixture.teams.away.winner == true -> {
                    if (isHomeTeamAtHome) awayWins++ else homeWins++
                }
                else -> draws++
            }

            // Accumulate goals (adjust based on which team was home/away)
            if (isHomeTeamAtHome) {
                homeGoals += fixture.goals.home ?: 0
                awayGoals += fixture.goals.away ?: 0
            } else {
                homeGoals += fixture.goals.away ?: 0
                awayGoals += fixture.goals.home ?: 0
            }
        }

        val totalMatches = headToHeadFixtures.size
        val homeWinRate = homeWins.toDouble() / totalMatches
        val awayWinRate = awayWins.toDouble() / totalMatches

        val favoredTeam = when {
            homeWinRate > awayWinRate + 0.2 -> headToHeadFixtures.first().teams.let { teams ->
                if (teams.home.id == homeTeamId) teams.home.name else teams.away.name
            }
            awayWinRate > homeWinRate + 0.2 -> headToHeadFixtures.first().teams.let { teams ->
                if (teams.away.id == awayTeamId) teams.away.name else teams.home.name
            }
            else -> null
        }

        return Factor(
            name = "Head-to-Head",
            weight = HEAD_TO_HEAD_WEIGHT,
            favoredTeam = favoredTeam,
            description = "Based on last $totalMatches meetings: $homeWins-$draws-$awayWins",
            data = mapOf(
                "homeWins" to homeWins,
                "awayWins" to awayWins,
                "draws" to draws,
                "homeGoals" to homeGoals,
                "awayGoals" to awayGoals,
                "matchesAnalyzed" to totalMatches,
                "homeWinRate" to homeWinRate,
                "awayWinRate" to awayWinRate
            )
        )
    }

    /**
     * Calculates home advantage factor
     */
    private fun calculateHomeAdvantage(homeTeam: Team, awayTeam: Team): Factor {
        // Base home advantage - most teams have some advantage at home
        val baseAdvantage = HOME_ADVANTAGE_BOOST

        // Additional factors could include:
        // - Stadium capacity and atmosphere
        // - Travel distance for away team
        // - Altitude differences
        // - Climate differences

        val venueCapacity = homeTeam.venue?.capacity ?: 30000
        val capacityBonus = when {
            venueCapacity > 60000 -> 0.05 // Large stadium bonus
            venueCapacity > 40000 -> 0.03 // Medium stadium bonus
            else -> 0.0
        }

        val totalAdvantage = baseAdvantage + capacityBonus

        return Factor(
            name = "Home Advantage",
            weight = HOME_ADVANTAGE_WEIGHT,
            favoredTeam = homeTeam.name,
            description = "Playing at ${homeTeam.venue?.name ?: "home"} provides advantage",
            data = mapOf(
                "baseAdvantage" to baseAdvantage,
                "capacityBonus" to capacityBonus,
                "totalAdvantage" to totalAdvantage,
                "venue" to (homeTeam.venue?.name ?: "Unknown"),
                "capacity" to venueCapacity
            )
        )
    }

    /**
     * Assesses player strength comparison between teams
     */
    private fun assessPlayerStrength(homeTeam: TeamAnalysisData, awayTeam: TeamAnalysisData): Factor {
        val homeStrength = calculateTeamPlayerStrength(homeTeam.players)
        val awayStrength = calculateTeamPlayerStrength(awayTeam.players)

        val strengthDifference = homeStrength - awayStrength
        val favoredTeam = when {
            strengthDifference > 0.15 -> homeTeam.team.name
            strengthDifference < -0.15 -> awayTeam.team.name
            else -> null
        }

        return Factor(
            name = "Player Strength",
            weight = PLAYER_STRENGTH_WEIGHT,
            favoredTeam = favoredTeam,
            description = when {
                strengthDifference > 0.15 -> "${homeTeam.team.name} has stronger squad"
                strengthDifference < -0.15 -> "${awayTeam.team.name} has stronger squad"
                else -> "Teams have similar squad strength"
            },
            data = mapOf(
                "homeStrength" to homeStrength,
                "awayStrength" to awayStrength,
                "strengthDifference" to strengthDifference
            )
        )
    }

    /**
     * Calculates team player strength based on player statistics
     */
    private fun calculateTeamPlayerStrength(players: List<PlayerDetail>): Double {
        if (players.isEmpty()) return 0.5

        val playerStrengths = players.mapNotNull { player ->
            player.statistics?.firstOrNull()?.let { stats ->
                calculatePlayerStrength(stats)
            }
        }

        return if (playerStrengths.isNotEmpty()) {
            playerStrengths.average()
        } else {
            0.5
        }
    }

    /**
     * Calculates individual player strength score
     */
    private fun calculatePlayerStrength(stats: PlayerStatistics): Double {
        val rating = stats.games.rating?.toDoubleOrNull() ?: 6.5
        val appearances = stats.games.appearences ?: 0
        val goals = stats.goals?.total ?: 0
        val assists = stats.goals?.assists ?: 0

        // Normalize rating (typically 1-10 scale)
        val normalizedRating = (rating - 1) / 9

        // Weight by appearances (more appearances = more reliable data)
        val appearanceWeight = minOf(appearances / 20.0, 1.0)

        // Goal/assist bonus
        val attackBonus = (goals + assists) * 0.01

        return (normalizedRating * appearanceWeight + attackBonus).coerceIn(0.0, 1.0)
    }

    /**
     * Evaluates impact of missing key players
     */
    private fun evaluateAbsenceImpact(homeTeam: TeamAnalysisData, awayTeam: TeamAnalysisData): Factor {
        val homeImpact = calculateAbsenceImpact(homeTeam.players)
        val awayImpact = calculateAbsenceImpact(awayTeam.players)

        val impactDifference = awayImpact - homeImpact // Higher away impact favors home team
        val favoredTeam = when {
            impactDifference > 0.1 -> homeTeam.team.name
            impactDifference < -0.1 -> awayTeam.team.name
            else -> null
        }

        return Factor(
            name = "Absence Impact",
            weight = ABSENCE_IMPACT_WEIGHT,
            favoredTeam = favoredTeam,
            description = when {
                impactDifference > 0.1 -> "${awayTeam.team.name} missing key players"
                impactDifference < -0.1 -> "${homeTeam.team.name} missing key players"
                else -> "No significant absences"
            },
            data = mapOf(
                "homeImpact" to homeImpact,
                "awayImpact" to awayImpact,
                "impactDifference" to impactDifference
            )
        )
    }

    /**
     * Calculates absence impact for a team
     */
    private fun calculateAbsenceImpact(players: List<PlayerDetail>): Double {
        val injuredPlayers = players.filter { it.injured }
        if (injuredPlayers.isEmpty()) return 0.0

        return injuredPlayers.sumOf { player ->
            player.statistics?.firstOrNull()?.let { stats ->
                val importance = calculatePlayerImportance(stats)
                importance * 0.2 // Max 20% impact per player
            } ?: 0.0
        }.coerceAtMost(0.5) // Cap total impact at 50%
    }

    /**
     * Calculates player importance to the team
     */
    private fun calculatePlayerImportance(stats: PlayerStatistics): Double {
        val appearances = stats.games.appearences ?: 0
        val lineups = stats.games.lineups ?: 0
        val rating = stats.games.rating?.toDoubleOrNull() ?: 6.5
        val goals = stats.goals?.total ?: 0
        val assists = stats.goals?.assists ?: 0

        val playTimeRatio = if (appearances > 0) lineups.toDouble() / appearances else 0.0
        val performanceScore = (rating - 6.0) / 4.0 // Normalize around 6.0 average
        val contributionScore = (goals + assists) * 0.05

        return (playTimeRatio * 0.5 + performanceScore * 0.3 + contributionScore * 0.2).coerceIn(0.0, 1.0)
    }

    /**
     * Combines all factors to generate final match prediction
     */
    private fun combineFactors(
        homeTeamData: TeamAnalysisData,
        awayTeamData: TeamAnalysisData,
        recentFormFactor: Factor,
        headToHeadFactor: Factor,
        homeAdvantageFactor: Factor,
        playerStrengthFactor: Factor,
        absenceImpactFactor: Factor,
        standings: List<Standing>,
        fixtureId: Int
    ): MatchPrediction {

        // Calculate weighted score for home team advantage
        var homeAdvantageScore = 0.0

        // Recent form contribution
        val homeFormScore = recentFormFactor.data?.get("homeFormScore") as? Double ?: 0.5
        val awayFormScore = recentFormFactor.data?.get("awayFormScore") as? Double ?: 0.5
        homeAdvantageScore += (homeFormScore - awayFormScore) * RECENT_FORM_WEIGHT

        // Head-to-head contribution
        val homeWinRate = headToHeadFactor.data?.get("homeWinRate") as? Double ?: 0.33
        val awayWinRate = headToHeadFactor.data?.get("awayWinRate") as? Double ?: 0.33
        homeAdvantageScore += (homeWinRate - awayWinRate) * HEAD_TO_HEAD_WEIGHT

        // Home advantage contribution
        homeAdvantageScore += HOME_ADVANTAGE_BOOST * HOME_ADVANTAGE_WEIGHT

        // Player strength contribution
        val strengthDiff = playerStrengthFactor.data?.get("strengthDifference") as? Double ?: 0.0
        homeAdvantageScore += strengthDiff * PLAYER_STRENGTH_WEIGHT

        // Absence impact contribution
        val absenceImpactDiff = absenceImpactFactor.data?.get("impactDifference") as? Double ?: 0.0
        homeAdvantageScore += absenceImpactDiff * ABSENCE_IMPACT_WEIGHT

        // League position contribution
        val positionAdvantage = calculatePositionAdvantage(standings, homeTeamData.team.id, awayTeamData.team.id)
        homeAdvantageScore += positionAdvantage * LEAGUE_POSITION_WEIGHT

        // Convert advantage score to probabilities
        val probabilities = calculateMatchProbabilities(homeAdvantageScore)

        // Calculate predicted goals
        val goalPrediction = calculateGoalPrediction(homeTeamData, awayTeamData, homeAdvantageScore)

        // Determine predicted winner
        val predictedWinner = when {
            probabilities.homeWin > probabilities.awayWin && probabilities.homeWin > probabilities.draw -> homeTeamData.team.name
            probabilities.awayWin > probabilities.homeWin && probabilities.awayWin > probabilities.draw -> awayTeamData.team.name
            else -> null // Draw or too close to call
        }

        // Calculate confidence based on probability spread
        val maxProb = maxOf(probabilities.homeWin, probabilities.draw, probabilities.awayWin)
        val confidence = ((maxProb - 0.33) / 0.67).coerceIn(0.0, 1.0)

        return MatchPrediction(
            fixtureId = fixtureId,
            homeTeamId = homeTeamData.team.id,
            awayTeamId = awayTeamData.team.id,
            homeTeamName = homeTeamData.team.name,
            awayTeamName = awayTeamData.team.name,
            homeWinProbability = probabilities.homeWin,
            drawProbability = probabilities.draw,
            awayWinProbability = probabilities.awayWin,
            predictedHomeGoals = goalPrediction.homeGoals,
            predictedAwayGoals = goalPrediction.awayGoals,
            predictedWinner = predictedWinner,
            confidence = confidence,
            predictionFactors = PredictionFactors(
                headToHeadFactor = headToHeadFactor,
                recentFormFactor = recentFormFactor,
                homeAdvantage = homeAdvantageFactor,
                playerStrength = playerStrengthFactor,
                absenceImpact = absenceImpactFactor
            ),
            additionalStats = mapOf(
                "homeAdvantageScore" to homeAdvantageScore,
                "positionAdvantage" to positionAdvantage,
                "confidenceLevel" to when {
                    confidence > 0.8 -> "Very High"
                    confidence > 0.6 -> "High"
                    confidence > 0.4 -> "Medium"
                    else -> "Low"
                }
            )
        )
    }

    /**
     * Calculates position-based advantage from league standings
     */
    private fun calculatePositionAdvantage(standings: List<Standing>, homeTeamId: Int, awayTeamId: Int): Double {
        if (standings.isEmpty()) return 0.0

        val homePosition = standings.find { it.team.id == homeTeamId }?.rank ?: (standings.size / 2)
        val awayPosition = standings.find { it.team.id == awayTeamId }?.rank ?: (standings.size / 2)

        // Lower rank number = better position
        val positionDifference = awayPosition - homePosition
        return (positionDifference.toDouble() / standings.size).coerceIn(-0.3, 0.3)
    }

    /**
     * Converts advantage score to match outcome probabilities
     */
    private fun calculateMatchProbabilities(homeAdvantageScore: Double): MatchProbabilities {
        // Use logistic function to convert advantage score to probabilities
        val homeWinBase = 1.0 / (1.0 + exp(-homeAdvantageScore * 5))
        val awayWinBase = 1.0 / (1.0 + exp(homeAdvantageScore * 5))

        // Ensure probabilities sum to 1.0
        val total = homeWinBase + awayWinBase + 0.25 // Base draw probability

        return MatchProbabilities(
            homeWin = (homeWinBase / total).coerceIn(0.1, 0.8),
            draw = (0.25 / total).coerceIn(0.1, 0.5),
            awayWin = (awayWinBase / total).coerceIn(0.1, 0.8)
        ).normalize()
    }

    /**
     * Calculates predicted goals for both teams
     */
    private fun calculateGoalPrediction(
        homeTeam: TeamAnalysisData,
        awayTeam: TeamAnalysisData,
        homeAdvantageScore: Double
    ): GoalPrediction {
        // Base expected goals from team statistics
        val homeAttackStrength = calculateAttackStrength(homeTeam.statistics)
        val homeDefenseStrength = calculateDefenseStrength(homeTeam.statistics)
        val awayAttackStrength = calculateAttackStrength(awayTeam.statistics)
        val awayDefenseStrength = calculateDefenseStrength(awayTeam.statistics)

        // Calculate expected goals using Poisson model
        val homeExpectedGoals = (homeAttackStrength / awayDefenseStrength) * LEAGUE_AVERAGE_GOALS * POISSON_LAMBDA_ADJUSTMENT
        val awayExpectedGoals = (awayAttackStrength / homeDefenseStrength) * LEAGUE_AVERAGE_GOALS * POISSON_LAMBDA_ADJUSTMENT

        // Apply home advantage
        val homeGoalsAdjusted = homeExpectedGoals * (1 + homeAdvantageScore * 0.2)
        val awayGoalsAdjusted = awayExpectedGoals * (1 - homeAdvantageScore * 0.1)

        return GoalPrediction(
            homeGoals = homeGoalsAdjusted.coerceIn(0.1, 6.0),
            awayGoals = awayGoalsAdjusted.coerceIn(0.1, 6.0)
        )
    }

    /**
     * Calculates team's attack strength relative to league average
     */
    private fun calculateAttackStrength(teamStats: TeamStatistics): Double {
        val goalsScored = teamStats.goals.scored.total
        val matchesPlayed = teamStats.fixtures.played.total

        return if (matchesPlayed > 0) {
            (goalsScored.toDouble() / matchesPlayed) / LEAGUE_AVERAGE_GOALS
        } else {
            1.0 // League average if no data
        }
    }

    /**
     * Calculates team's defense strength relative to league average
     */
    private fun calculateDefenseStrength(teamStats: TeamStatistics): Double {
        val goalsConceded = teamStats.goals.conceded.total
        val matchesPlayed = teamStats.fixtures.played.total

        return if (matchesPlayed > 0) {
            (goalsConceded.toDouble() / matchesPlayed) / LEAGUE_AVERAGE_GOALS
        } else {
            1.0 // League average if no data
        }
    }
}

/**
 * Data class to hold all team analysis data
 */
data class TeamAnalysisData(
    val team: Team,
    val statistics: TeamStatistics,
    val recentFixtures: List<Fixture>,
    val players: List<PlayerDetail>
)

/**
 * Data class for match outcome probabilities
 */
data class MatchProbabilities(
    val homeWin: Double,
    val draw: Double,
    val awayWin: Double
) {
    /**
     * Normalizes probabilities to ensure they sum to 1.0
     */
    fun normalize(): MatchProbabilities {
        val total = homeWin + draw + awayWin
        return if (total > 0) {
            MatchProbabilities(
                homeWin = homeWin / total,
                draw = draw / total,
                awayWin = awayWin / total
            )
        } else {
            MatchProbabilities(0.33, 0.34, 0.33) // Equal probabilities as fallback
        }
    }
}

/**
 * Data class for goal predictions
 */
data class GoalPrediction(
    val homeGoals: Double,
    val awayGoals: Double
)