package com.example.anna.data.repository

import com.example.anna.data.api.FootballApiService
import com.example.anna.data.model.ApiResponse
import com.example.anna.data.model.Fixture
import com.example.anna.data.model.LeagueWithSeasons
import com.example.anna.data.model.PlayerDetail
import com.example.anna.data.model.Standing
import com.example.anna.data.model.Team
import com.example.anna.data.model.TeamStatistics
import com.example.anna.util.Constants
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FootballRepository @Inject constructor(
    private val apiService: FootballApiService
) {
    // This would typically come from BuildConfig or a secure storage
    // For development purposes, it's hardcoded here
    // For production, you should move this to build.gradle or secure storage
    private val API_KEY = "YOUR_API_FOOTBALL_KEY_HERE"
    
    suspend fun getLeagues(
        leagueId: Int? = null,
        country: String? = null,
        current: Boolean? = true
    ): Response<ApiResponse<LeagueWithSeasons>> {
        return apiService.getLeagues(
            apiKey = API_KEY,
            leagueId = leagueId,
            country = country,
            current = current
        )
    }
    
    suspend fun getTeams(
        league: Int? = null,
        season: String = Constants.DEFAULT_SEASON,
        teamId: Int? = null,
        country: String? = null
    ): Response<ApiResponse<Team>> {
        return apiService.getTeams(
            apiKey = API_KEY,
            league = league,
            season = season,
            teamId = teamId,
            country = country
        )
    }

    suspend fun getTeamStatistics(
        leagueId: Int,
        season: String = Constants.DEFAULT_SEASON,
        teamId: Int
    ): Response<ApiResponse<TeamStatistics>> {
        return apiService.getTeamStatistics(
            apiKey = API_KEY,
            leagueId = leagueId,
            season = season,
            teamId = teamId
        )
    }
    
    suspend fun getFixtures(
        league: Int? = null,
        season: String = Constants.DEFAULT_SEASON,
        teamId: Int? = null,
        date: String? = null,
        status: String? = null
    ): Response<ApiResponse<Fixture>> {
        return apiService.getFixtures(
            apiKey = API_KEY,
            league = league,
            season = season,
            teamId = teamId,
            date = date,
            status = status
        )
    }

    suspend fun getFixtureStatistics(
        fixtureId: Int
    ): Response<ApiResponse<Fixture>> {
        return apiService.getFixtureStatistics(
            apiKey = API_KEY,
            fixtureId = fixtureId
        )
    }
    
    suspend fun getHeadToHead(
        team1Id: Int,
        team2Id: Int,
        fromDate: String? = null,
        toDate: String? = null
    ): Response<ApiResponse<Fixture>> {
        val h2hParam = "$team1Id-$team2Id"
        return apiService.getHeadToHead(
            apiKey = API_KEY,
            h2h = h2hParam,
            from = fromDate,
            to = toDate
        )
    }
    
    suspend fun getPlayers(
        teamId: Int,
        season: String = Constants.DEFAULT_SEASON,
        playerId: Int? = null
    ): Response<ApiResponse<PlayerDetail>> {
        return apiService.getPlayers(
            apiKey = API_KEY,
            teamId = teamId,
            season = season,
            playerId = playerId
        )
    }
    
    suspend fun getStandings(
        leagueId: Int,
        season: String = Constants.DEFAULT_SEASON
    ): Response<ApiResponse<List<Standing>>> {
        return apiService.getStandings(
            apiKey = API_KEY,
            leagueId = leagueId,
            season = season
        )
    }
}
