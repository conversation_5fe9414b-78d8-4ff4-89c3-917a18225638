package com.example.anna.data.model

data class Team(
    val id: Int,
    val name: String,
    val code: String?,
    val country: String,
    val founded: Int?,
    val national: Boolean,
    val logo: String,
    val venue: TeamVenue?
)

data class TeamVenue(
    val id: Int?,
    val name: String?,
    val address: String?,
    val city: String?,
    val capacity: Int?,
    val surface: String?,
    val image: String?
)

data class TeamStatistics(
    val team: Team,
    val form: String?, // Last X matches, e.g., "WWLDW"
    val fixtures: FixtureStats,
    val goals: GoalStats,
    val lineups: List<FormationStats>?,
    val cards: CardStats
)

data class FixtureStats(
    val played: Played,
    val wins: Played,
    val draws: Played,
    val loses: Played
)

data class Played(
    val home: Int,
    val away: Int,
    val total: Int
)

data class GoalStats(
    val scored: ScoredGoals,
    val conceded: ScoredGoals,
)

data class ScoredGoals(
    val home: Int,
    val away: Int,
    val total: Int
)

data class FormationStats(
    val formation: String,
    val played: Int
)

data class CardStats(
    val yellow: CardDetail,
    val red: CardDetail
)

data class CardDetail(
    val total: Int,
    val minuteDistribution: Map<String, Int>? // e.g., "0-15" -> 3, "16-30" -> 2
)
