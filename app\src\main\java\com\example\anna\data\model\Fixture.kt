package com.example.anna.data.model

import java.util.Date

/**
 * Represents a soccer match fixture
 */
data class Fixture(
    val id: Int,
    val referee: String?,
    val timezone: String,
    val date: String,
    val timestamp: Long,
    val periods: Periods,
    val venue: Venue,
    val status: Status,
    val league: League,
    val teams: Teams,
    val goals: Goals,
    val score: Score,
    val events: List<Event>?,
    val lineups: List<Lineup>?,
    val statistics: List<FixtureStatistics>?
)

data class Periods(
    val first: Long?,
    val second: Long?
)

data class Venue(
    val id: Int?,
    val name: String?,
    val city: String?
)

data class Status(
    val long: String,
    val short: String,
    val elapsed: Int?
)

data class Teams(
    val home: TeamMatchInfo,
    val away: TeamMatchInfo
)

data class TeamMatchInfo(
    val id: Int,
    val name: String,
    val logo: String,
    val winner: Boolean?
)

data class Goals(
    val home: Int?,
    val away: Int?
)

data class Score(
    val halftime: Goals?,
    val fulltime: Goals?,
    val extratime: Goals?,
    val penalty: Goals?
)

data class Event(
    val time: EventTime,
    val team: TeamMatchInfo,
    val player: EventPlayer,
    val assist: EventPlayer?,
    val type: String, // Goal, Card, Substitution
    val detail: String
)

data class EventTime(
    val elapsed: Int,
    val extra: Int?
)

data class EventPlayer(
    val id: Int?,
    val name: String?
)

data class Lineup(
    val team: TeamMatchInfo,
    val coach: Coach,
    val formation: String?,
    val startXI: List<Player>,
    val substitutes: List<Player>
)

data class Coach(
    val id: Int?,
    val name: String?,
    val photo: String?
)

data class Player(
    val id: Int,
    val name: String,
    val number: Int?,
    val pos: String?,
    val grid: String?
)

data class FixtureStatistics(
    val team: TeamMatchInfo,
    val statistics: List<Statistic>
)

data class Statistic(
    val type: String, // e.g., "Shots on Goal", "Total passes", etc.
    val value: Any? // Can be Int, String, or Double depending on the statistic
)
