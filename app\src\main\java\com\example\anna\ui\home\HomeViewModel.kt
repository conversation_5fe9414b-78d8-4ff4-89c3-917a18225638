package com.example.anna.ui.home

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.anna.data.model.Fixture
import com.example.anna.data.repository.FootballRepository
import com.example.anna.util.Constants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class FixtureUiState {
    object Loading : FixtureUiState()
    data class Success(val fixtures: List<Fixture>) : FixtureUiState()
    data class Error(val message: String) : FixtureUiState()
}

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val footballRepository: FootballRepository
) : ViewModel() {

    private val _fixturesState = MutableLiveData<FixtureUiState>(FixtureUiState.Loading)
    val fixturesState: LiveData<FixtureUiState> = _fixturesState

    fun fetchUpcomingFixtures(leagueId: Int? = null, date: String? = null, status: String = Constants.FIXTURE_STATUS_NOT_STARTED) {
        _fixturesState.value = FixtureUiState.Loading
        viewModelScope.launch {
            try {
                // Example: Fetch fixtures for a specific league or date
                // You might want to fetch for today or upcoming week by default
                val response = footballRepository.getFixtures(
                    league = leagueId, // e.g., Premier League ID
                    season = Constants.DEFAULT_SEASON, // Current season
                    date = date, // e.g., today's date in YYYY-MM-DD
                    status = status // Fetch 'Not Started' fixtures
                )

                if (response.isSuccessful) {
                    val fixtures = response.body()?.response ?: emptyList()
                    if (fixtures.isNotEmpty()) {
                        _fixturesState.value = FixtureUiState.Success(fixtures)
                    } else {
                        _fixturesState.value = FixtureUiState.Error("No upcoming fixtures found.")
                    }
                } else {
                    _fixturesState.value = FixtureUiState.Error("Error fetching fixtures: ${response.message()}")
                }
            } catch (e: Exception) {
                _fixturesState.value = FixtureUiState.Error("Network error: ${e.message}")
            }
        }
    }
}
