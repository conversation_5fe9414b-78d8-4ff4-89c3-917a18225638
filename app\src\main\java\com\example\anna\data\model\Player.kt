package com.example.anna.data.model

data class PlayerDetail(
    val id: Int,
    val name: String,
    val firstname: String?,
    val lastname: String?,
    val age: Int?,
    val birth: Birth?,
    val nationality: String?,
    val height: String?,
    val weight: String?,
    val injured: <PERSON><PERSON><PERSON>,
    val photo: String,
    val statistics: List<PlayerStatistics>?
)

data class Birth(
    val date: String?,
    val place: String?,
    val country: String?
)

data class PlayerStatistics(
    val team: TeamMatchInfo,
    val league: League,
    val games: PlayerGames,
    val substitutes: PlayerSubstitutes,
    val shots: PlayerShots?,
    val goals: PlayerGoals?,
    val passes: PlayerPasses?,
    val tackles: PlayerTackles?,
    val duels: PlayerDuels?,
    val dribbles: PlayerDribbles?,
    val fouls: PlayerFouls?,
    val cards: PlayerCards?,
    val penalty: PlayerPenalty?
)

data class PlayerGames(
    val appearences: Int?,
    val lineups: Int?,
    val minutes: Int?,
    val number: Int?,
    val position: String?,
    val rating: String?,
    val captain: <PERSON><PERSON><PERSON>?
)

data class PlayerSubstitutes(
    val substitutesIn: Int?,
    val substitutesOut: Int?,
    val substitutesOnBench: Int?
)

data class PlayerShots(
    val total: Int?,
    val on: Int?
)

data class PlayerGoals(
    val total: Int?,
    val conceded: Int?,
    val assists: Int?,
    val saves: Int?
)

data class PlayerPasses(
    val total: Int?,
    val key: Int?,
    val accuracy: Int?
)

data class PlayerTackles(
    val total: Int?,
    val blocks: Int?,
    val interceptions: Int?
)

data class PlayerDuels(
    val total: Int?,
    val won: Int?
)

data class PlayerDribbles(
    val attempts: Int?,
    val success: Int?,
    val past: Int?
)

data class PlayerFouls(
    val drawn: Int?,
    val committed: Int?
)

data class PlayerCards(
    val yellow: Int?,
    val yellowred: Int?,
    val red: Int?
)

data class PlayerPenalty(
    val won: Int?,
    val committed: Int?,
    val scored: Int?,
    val missed: Int?,
    val saved: Int?
)

// Prediction specific classes
data class PlayerForm(
    val playerId: Int,
    val recentRating: Double, // Average of recent matches
    val goalsScored: Int,
    val assists: Int,
    val minutesPlayed: Int,
    val isInjured: Boolean,
    val isSuspended: Boolean
)
