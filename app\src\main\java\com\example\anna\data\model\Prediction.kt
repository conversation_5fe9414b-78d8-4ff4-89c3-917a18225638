package com.example.anna.data.model

/**
 * Class representing a match prediction with detailed analysis
 */
data class MatchPrediction(
    val fixtureId: Int,
    val homeTeamId: Int,
    val awayTeamId: Int,
    val homeTeamName: String,
    val awayTeamName: String,
    val homeWinProbability: Double,  // 0 to 1
    val drawProbability: Double,     // 0 to 1
    val awayWinProbability: Double,  // 0 to 1
    val predictedHomeGoals: Double,
    val predictedAwayGoals: Double,
    val predictedWinner: String?,    // Team name or null for draw
    val confidence: Double,          // 0 to 1, how confident is the prediction
    val predictionFactors: PredictionFactors,
    val additionalStats: Map<String, Any> // Other predictions like corners, cards, etc.
)

/**
 * Factors that influenced the prediction
 */
data class PredictionFactors(
    val headToHeadFactor: Factor,
    val recentFormFactor: Factor,
    val homeAdvantage: Factor,
    val playerStrength: Factor,
    val absenceImpact: Factor
)

/**
 * A specific factor that influenced the prediction
 */
data class Factor(
    val name: String,
    val weight: Double,  // How much this factor weighs in the overall prediction
    val favoredTeam: String?,  // Which team this factor favors, or null if neutral
    val description: String,  // Human-readable description of this factor
    val data: Map<String, Any>?  // Additional data related to this factor
)

/**
 * Model class used by UI to display prediction summary
 */
data class PredictionSummary(
    val matchId: Int,
    val homeTeam: String,
    val awayTeam: String,
    val homeTeamLogo: String,
    val awayTeamLogo: String,
    val prediction: String,  // "Home Win", "Away Win", "Draw"
    val score: String,       // "2-1", "0-0", etc.
    val confidence: Int,     // 0-100%
    val league: String,
    val matchTime: String,
    val topFactor: String    // Most influential factor in simple terms
)
