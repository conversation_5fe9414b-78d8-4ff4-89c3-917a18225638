<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <TextView
            android:id="@+id/tvHomeTeam"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:gravity="start"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvScoreTime"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Manchester United" />

        <TextView
            android:id="@+id/tvAwayTeam"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:gravity="start"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvScoreTime"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvHomeTeam"
            tools:text="Liverpool FC" />

        <TextView
            android:id="@+id/tvScoreTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/tvAwayTeam"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvHomeTeam"
            tools:text="15:00" />

        <TextView
            android:id="@+id/tvLeague"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvAwayTeam"
            tools:text="Premier League" />

        <TextView
            android:id="@+id/tvMatchDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvScoreTime"
            tools:text="2024-12-25" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
