package com.example.anna.data.repository

import com.example.anna.data.model.*
import com.example.anna.util.Constants
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import retrofit2.Response

class PredictionEngineTest {

    private lateinit var predictionEngine: PredictionEngine
    private lateinit var mockFootballRepository: FootballRepository

    @Before
    fun setup() {
        mockFootballRepository = mockk()
        predictionEngine = PredictionEngine(mockFootballRepository)
    }

    @Test
    fun `generatePrediction returns successful result with valid data`() = runBlocking {
        // Arrange
        val homeTeamId = 1
        val awayTeamId = 2
        val leagueId = 39
        val season = "2024"

        setupMockResponses(homeTeamId, awayTeamId, leagueId, season)

        // Act
        val result = predictionEngine.generatePrediction(homeTeamId, awayTeamId, leagueId, season)

        // Assert
        assertTrue("Prediction should be successful", result.isSuccess)
        
        val prediction = result.getOrNull()
        assertNotNull("Prediction should not be null", prediction)
        
        prediction?.let {
            assertEquals("Home team ID should match", homeTeamId, it.homeTeamId)
            assertEquals("Away team ID should match", awayTeamId, it.awayTeamId)
            assertTrue("Home win probability should be valid", it.homeWinProbability in 0.0..1.0)
            assertTrue("Draw probability should be valid", it.drawProbability in 0.0..1.0)
            assertTrue("Away win probability should be valid", it.awayWinProbability in 0.0..1.0)
            assertTrue("Confidence should be valid", it.confidence in 0.0..1.0)
            
            // Probabilities should sum to approximately 1.0
            val totalProbability = it.homeWinProbability + it.drawProbability + it.awayWinProbability
            assertTrue("Probabilities should sum to ~1.0", kotlin.math.abs(totalProbability - 1.0) < 0.01)
        }
    }

    @Test
    fun `generatePrediction handles missing data gracefully`() = runBlocking {
        // Arrange
        val homeTeamId = 1
        val awayTeamId = 2
        val leagueId = 39
        val season = "2024"

        setupMockResponsesWithMissingData(homeTeamId, awayTeamId, leagueId, season)

        // Act
        val result = predictionEngine.generatePrediction(homeTeamId, awayTeamId, leagueId, season)

        // Assert
        assertTrue("Prediction should still succeed with missing data", result.isSuccess)
        
        val prediction = result.getOrNull()
        assertNotNull("Prediction should not be null", prediction)
    }

    @Test
    fun `generatePrediction handles API failures`() = runBlocking {
        // Arrange
        val homeTeamId = 1
        val awayTeamId = 2
        val leagueId = 39
        val season = "2024"

        // Mock API failures
        coEvery { 
            mockFootballRepository.getTeamStatistics(any(), any(), any()) 
        } returns Response.error(404, mockk(relaxed = true))

        // Act
        val result = predictionEngine.generatePrediction(homeTeamId, awayTeamId, leagueId, season)

        // Assert
        assertTrue("Prediction should fail when required data is unavailable", result.isFailure)
    }

    private fun setupMockResponses(homeTeamId: Int, awayTeamId: Int, leagueId: Int, season: String) {
        // Mock team statistics
        val homeTeamStats = createMockTeamStatistics(homeTeamId, "Home Team")
        val awayTeamStats = createMockTeamStatistics(awayTeamId, "Away Team")
        
        coEvery { 
            mockFootballRepository.getTeamStatistics(leagueId, season, homeTeamId) 
        } returns Response.success(ApiResponse(
            get = "teams/statistics",
            parameters = mapOf(),
            errors = emptyList(),
            results = 1,
            paging = Paging(1, 1),
            response = listOf(homeTeamStats)
        ))
        
        coEvery { 
            mockFootballRepository.getTeamStatistics(leagueId, season, awayTeamId) 
        } returns Response.success(ApiResponse(
            get = "teams/statistics",
            parameters = mapOf(),
            errors = emptyList(),
            results = 1,
            paging = Paging(1, 1),
            response = listOf(awayTeamStats)
        ))

        // Mock fixtures
        coEvery { 
            mockFootballRepository.getFixtures(leagueId, season, homeTeamId, any(), any()) 
        } returns Response.success(ApiResponse(
            get = "fixtures",
            parameters = mapOf(),
            errors = emptyList(),
            results = 5,
            paging = Paging(1, 1),
            response = createMockFixtures(homeTeamId)
        ))
        
        coEvery { 
            mockFootballRepository.getFixtures(leagueId, season, awayTeamId, any(), any()) 
        } returns Response.success(ApiResponse(
            get = "fixtures",
            parameters = mapOf(),
            errors = emptyList(),
            results = 5,
            paging = Paging(1, 1),
            response = createMockFixtures(awayTeamId)
        ))

        // Mock players
        coEvery { 
            mockFootballRepository.getPlayers(homeTeamId, season, any()) 
        } returns Response.success(ApiResponse(
            get = "players",
            parameters = mapOf(),
            errors = emptyList(),
            results = 2,
            paging = Paging(1, 1),
            response = createMockPlayers(homeTeamId)
        ))
        
        coEvery { 
            mockFootballRepository.getPlayers(awayTeamId, season, any()) 
        } returns Response.success(ApiResponse(
            get = "players",
            parameters = mapOf(),
            errors = emptyList(),
            results = 2,
            paging = Paging(1, 1),
            response = createMockPlayers(awayTeamId)
        ))

        // Mock head-to-head
        coEvery { 
            mockFootballRepository.getHeadToHead(homeTeamId, awayTeamId, any(), any()) 
        } returns Response.success(ApiResponse(
            get = "fixtures/headtohead",
            parameters = mapOf(),
            errors = emptyList(),
            results = 3,
            paging = Paging(1, 1),
            response = createMockHeadToHeadFixtures(homeTeamId, awayTeamId)
        ))

        // Mock standings
        coEvery { 
            mockFootballRepository.getStandings(leagueId, season) 
        } returns Response.success(ApiResponse(
            get = "standings",
            parameters = mapOf(),
            errors = emptyList(),
            results = 1,
            paging = Paging(1, 1),
            response = listOf(createMockStandings(homeTeamId, awayTeamId))
        ))
    }

    private fun setupMockResponsesWithMissingData(homeTeamId: Int, awayTeamId: Int, leagueId: Int, season: String) {
        // Setup minimal responses that will still allow prediction to work
        val homeTeamStats = createMockTeamStatistics(homeTeamId, "Home Team")
        val awayTeamStats = createMockTeamStatistics(awayTeamId, "Away Team")
        
        coEvery { 
            mockFootballRepository.getTeamStatistics(leagueId, season, homeTeamId) 
        } returns Response.success(ApiResponse(
            get = "teams/statistics",
            parameters = mapOf(),
            errors = emptyList(),
            results = 1,
            paging = Paging(1, 1),
            response = listOf(homeTeamStats)
        ))
        
        coEvery { 
            mockFootballRepository.getTeamStatistics(leagueId, season, awayTeamId) 
        } returns Response.success(ApiResponse(
            get = "teams/statistics",
            parameters = mapOf(),
            errors = emptyList(),
            results = 1,
            paging = Paging(1, 1),
            response = listOf(awayTeamStats)
        ))

        // Return empty responses for optional data
        coEvery { 
            mockFootballRepository.getFixtures(any(), any(), any(), any(), any()) 
        } returns Response.success(ApiResponse(
            get = "fixtures",
            parameters = mapOf(),
            errors = emptyList(),
            results = 0,
            paging = Paging(1, 1),
            response = emptyList()
        ))
        
        coEvery { 
            mockFootballRepository.getPlayers(any(), any(), any()) 
        } returns Response.success(ApiResponse(
            get = "players",
            parameters = mapOf(),
            errors = emptyList(),
            results = 0,
            paging = Paging(1, 1),
            response = emptyList()
        ))
        
        coEvery { 
            mockFootballRepository.getHeadToHead(any(), any(), any(), any()) 
        } returns Response.success(ApiResponse(
            get = "fixtures/headtohead",
            parameters = mapOf(),
            errors = emptyList(),
            results = 0,
            paging = Paging(1, 1),
            response = emptyList()
        ))
        
        coEvery { 
            mockFootballRepository.getStandings(any(), any()) 
        } returns Response.success(ApiResponse(
            get = "standings",
            parameters = mapOf(),
            errors = emptyList(),
            results = 0,
            paging = Paging(1, 1),
            response = emptyList()
        ))
    }

    private fun createMockTeamStatistics(teamId: Int, teamName: String): TeamStatistics {
        return TeamStatistics(
            team = Team(
                id = teamId,
                name = teamName,
                code = teamName.take(3).uppercase(),
                country = "England",
                founded = 1900,
                national = false,
                logo = "https://example.com/logo.png",
                venue = TeamVenue(
                    id = teamId * 10,
                    name = "$teamName Stadium",
                    address = "Stadium Address",
                    city = "City",
                    capacity = 50000,
                    surface = "grass",
                    image = "https://example.com/stadium.png"
                )
            ),
            form = "WWLDW",
            fixtures = FixtureStats(
                played = Played(home = 10, away = 10, total = 20),
                wins = Played(home = 6, away = 4, total = 10),
                draws = Played(home = 2, away = 3, total = 5),
                loses = Played(home = 2, away = 3, total = 5)
            ),
            goals = GoalStats(
                scored = ScoredGoals(home = 25, away = 20, total = 45),
                conceded = ScoredGoals(home = 15, away = 20, total = 35)
            ),
            lineups = listOf(
                FormationStats(formation = "4-3-3", played = 15),
                FormationStats(formation = "4-4-2", played = 5)
            ),
            cards = CardStats(
                yellow = CardDetail(total = 30, minuteDistribution = mapOf("0-15" to 5, "16-30" to 8)),
                red = CardDetail(total = 3, minuteDistribution = mapOf("45-60" to 2, "61-75" to 1))
            )
        )
    }

    private fun createMockFixtures(teamId: Int): List<Fixture> {
        return listOf(
            createMockFixture(1, teamId, 100, true, 2, 1),
            createMockFixture(2, teamId, 101, false, 1, 1),
            createMockFixture(3, teamId, 102, true, 3, 0),
            createMockFixture(4, teamId, 103, false, 0, 2),
            createMockFixture(5, teamId, 104, true, 1, 1)
        )
    }

    private fun createMockFixture(
        fixtureId: Int,
        teamId: Int,
        opponentId: Int,
        isHome: Boolean,
        teamGoals: Int,
        opponentGoals: Int
    ): Fixture {
        val homeTeam = if (isHome) {
            TeamMatchInfo(teamId, "Team $teamId", "logo.png", teamGoals > opponentGoals)
        } else {
            TeamMatchInfo(opponentId, "Team $opponentId", "logo.png", opponentGoals > teamGoals)
        }

        val awayTeam = if (isHome) {
            TeamMatchInfo(opponentId, "Team $opponentId", "logo.png", opponentGoals > teamGoals)
        } else {
            TeamMatchInfo(teamId, "Team $teamId", "logo.png", teamGoals > opponentGoals)
        }

        return Fixture(
            id = fixtureId,
            referee = "Test Referee",
            timezone = "UTC",
            date = "2024-01-01T15:00:00+00:00",
            timestamp = 1704117600,
            periods = Periods(first = 1704117600, second = 1704121200),
            venue = Venue(id = 1, name = "Test Stadium", city = "Test City"),
            status = Status(long = "Match Finished", short = "FT", elapsed = 90),
            league = League(
                id = 39,
                name = "Premier League",
                type = "League",
                logo = "league.png",
                country = "England",
                countryCode = "GB",
                countryFlag = "flag.png",
                season = 2024,
                round = "Regular Season - 1"
            ),
            teams = Teams(home = homeTeam, away = awayTeam),
            goals = Goals(
                home = if (isHome) teamGoals else opponentGoals,
                away = if (isHome) opponentGoals else teamGoals
            ),
            score = Score(
                halftime = Goals(
                    home = if (isHome) teamGoals / 2 else opponentGoals / 2,
                    away = if (isHome) opponentGoals / 2 else teamGoals / 2
                ),
                fulltime = Goals(
                    home = if (isHome) teamGoals else opponentGoals,
                    away = if (isHome) opponentGoals else teamGoals
                ),
                extratime = null,
                penalty = null
            ),
            events = emptyList(),
            lineups = emptyList(),
            statistics = emptyList()
        )
    }

    private fun createMockPlayers(teamId: Int): List<PlayerDetail> {
        return listOf(
            PlayerDetail(
                id = teamId * 100 + 1,
                name = "Player One",
                firstname = "Player",
                lastname = "One",
                age = 25,
                birth = Birth(date = "1999-01-01", place = "City", country = "Country"),
                nationality = "English",
                height = "180cm",
                weight = "75kg",
                injured = false,
                photo = "player1.png",
                statistics = listOf(
                    PlayerStatistics(
                        team = TeamMatchInfo(teamId, "Team $teamId", "logo.png", null),
                        league = League(39, "Premier League", "League", "logo.png", "England", "GB", "flag.png", 2024, "1"),
                        games = PlayerGames(
                            appearences = 20,
                            lineups = 18,
                            minutes = 1620,
                            number = 10,
                            position = "Midfielder",
                            rating = "7.5",
                            captain = false
                        ),
                        substitutes = PlayerSubstitutes(substitutesIn = 2, substitutesOut = 5, substitutesOnBench = 3),
                        shots = PlayerShots(total = 45, on = 20),
                        goals = PlayerGoals(total = 8, conceded = null, assists = 5, saves = null),
                        passes = PlayerPasses(total = 1200, key = 45, accuracy = 85),
                        tackles = PlayerTackles(total = 60, blocks = 15, interceptions = 25),
                        duels = PlayerDuels(total = 150, won = 90),
                        dribbles = PlayerDribbles(attempts = 80, success = 50, past = 30),
                        fouls = PlayerFouls(drawn = 25, committed = 15),
                        cards = PlayerCards(yellow = 3, yellowred = 0, red = 0),
                        penalty = PlayerPenalty(won = 2, committed = 0, scored = 1, missed = 0, saved = null)
                    )
                )
            ),
            PlayerDetail(
                id = teamId * 100 + 2,
                name = "Player Two",
                firstname = "Player",
                lastname = "Two",
                age = 28,
                birth = Birth(date = "1996-05-15", place = "City", country = "Country"),
                nationality = "English",
                height = "185cm",
                weight = "80kg",
                injured = true, // This player is injured
                photo = "player2.png",
                statistics = listOf(
                    PlayerStatistics(
                        team = TeamMatchInfo(teamId, "Team $teamId", "logo.png", null),
                        league = League(39, "Premier League", "League", "logo.png", "England", "GB", "flag.png", 2024, "1"),
                        games = PlayerGames(
                            appearences = 15,
                            lineups = 12,
                            minutes = 1080,
                            number = 9,
                            position = "Forward",
                            rating = "8.2",
                            captain = true
                        ),
                        substitutes = PlayerSubstitutes(substitutesIn = 3, substitutesOut = 2, substitutesOnBench = 1),
                        shots = PlayerShots(total = 60, on = 35),
                        goals = PlayerGoals(total = 12, conceded = null, assists = 3, saves = null),
                        passes = PlayerPasses(total = 800, key = 25, accuracy = 78),
                        tackles = PlayerTackles(total = 20, blocks = 5, interceptions = 8),
                        duels = PlayerDuels(total = 100, won = 65),
                        dribbles = PlayerDribbles(attempts = 120, success = 80, past = 45),
                        fouls = PlayerFouls(drawn = 35, committed = 10),
                        cards = PlayerCards(yellow = 2, yellowred = 0, red = 0),
                        penalty = PlayerPenalty(won = 3, committed = 0, scored = 2, missed = 1, saved = null)
                    )
                )
            )
        )
    }

    private fun createMockHeadToHeadFixtures(homeTeamId: Int, awayTeamId: Int): List<Fixture> {
        return listOf(
            createMockFixture(201, homeTeamId, awayTeamId, true, 2, 1),
            createMockFixture(202, homeTeamId, awayTeamId, false, 1, 3),
            createMockFixture(203, homeTeamId, awayTeamId, true, 1, 1)
        )
    }

    private fun createMockStandings(homeTeamId: Int, awayTeamId: Int): List<Standing> {
        return listOf(
            Standing(
                rank = 3,
                team = TeamMatchInfo(homeTeamId, "Home Team", "logo.png", null),
                points = 45,
                goalsDiff = 15,
                group = "Premier League",
                form = "WWLDW",
                status = "same",
                description = null,
                all = TeamStats(played = 20, win = 12, draw = 5, lose = 3, goals = TeamGoals(for_ = 35, against = 20)),
                home = TeamStats(played = 10, win = 7, draw = 2, lose = 1, goals = TeamGoals(for_ = 20, against = 8)),
                away = TeamStats(played = 10, win = 5, draw = 3, lose = 2, goals = TeamGoals(for_ = 15, against = 12)),
                update = "2024-01-01T00:00:00+00:00"
            ),
            Standing(
                rank = 8,
                team = TeamMatchInfo(awayTeamId, "Away Team", "logo.png", null),
                points = 32,
                goalsDiff = 5,
                group = "Premier League",
                form = "LDWLW",
                status = "same",
                description = null,
                all = TeamStats(played = 20, win = 8, draw = 8, lose = 4, goals = TeamGoals(for_ = 28, against = 23)),
                home = TeamStats(played = 10, win = 5, draw = 4, lose = 1, goals = TeamGoals(for_ = 16, against = 10)),
                away = TeamStats(played = 10, win = 3, draw = 4, lose = 3, goals = TeamGoals(for_ = 12, against = 13)),
                update = "2024-01-01T00:00:00+00:00"
            )
        )
    }
}
